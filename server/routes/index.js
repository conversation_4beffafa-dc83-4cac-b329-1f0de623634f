const { Router } = require('express');
const { body, param, validationResult } = require('express-validator');
const crypto = require('crypto');
const { authenticateToken } = require('../middleware/auth');

function setupRoutes(app, dbService) {
  const router = Router();

  // Validation middleware
  const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ error: 'Invalid input data' });
    }
    next();
  };

  // Input sanitization
  const sanitizeTaskInput = [
    body('title').trim().isLength({ min: 1, max: 500 }).escape(),
    body('description').optional().trim().isLength({ max: 2000 }).escape(),
    body('dueDate').custom((value) => {
      // Accept both Date objects and YYYY-MM-DD strings
      if (typeof value === 'string') {
        if (!/^\d{4}-\d{2}-\d{2}$/.test(value)) {
          throw new Error('dueDate must be in YYYY-MM-DD format');
        }
        return true;
      }
      // If it's a Date object, convert it
      if (value instanceof Date || typeof value === 'object') {
        return true;
      }
      throw new Error('dueDate must be a valid date');
    }),
    body('dueTime').matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    body('priority').isIn(['Low', 'Medium', 'High']),
    body('category').optional().trim().isLength({ max: 100 }).escape(),
    body('isRecurring').isBoolean(),
    body('recurrenceType').optional({ nullable: true }).custom((value) => {
      if (value === null || value === undefined || value === '') {
        return true;
      }
      if (['Daily', 'Weekly', 'Monthly', 'Yearly'].includes(value)) {
        return true;
      }
      throw new Error('recurrenceType must be one of: Daily, Weekly, Monthly, Yearly');
    }),
    body('recurrenceInterval').optional({ nullable: true }).custom((value) => {
      if (value === null || value === undefined) {
        return true;
      }
      if (Number.isInteger(value) && value >= 1 && value <= 365) {
        return true;
      }
      throw new Error('recurrenceInterval must be a positive integer between 1 and 365');
    })
  ];

  // GET /api/tasks - Get all tasks
  router.get('/tasks', authenticateToken, async (req, res) => {
    try {
      const tasks = await dbService.getTasks();
      res.json({ tasks });
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch tasks' });
    }
  });

  // POST /api/tasks - Create new task
  router.post('/tasks', authenticateToken, sanitizeTaskInput, handleValidationErrors, async (req, res) => {
    try {
      // Handle dueDate whether it's a string or Date object
      let dueDateString;
      if (typeof req.body.dueDate === 'string') {
        dueDateString = req.body.dueDate;
      } else if (req.body.dueDate instanceof Date) {
        dueDateString = req.body.dueDate.toISOString().split('T')[0];
      } else {
        dueDateString = new Date(req.body.dueDate).toISOString().split('T')[0];
      }

      const taskData = {
        ...req.body,
        id: crypto.randomUUID(),
        isCompleted: false,
        completedAt: null,
        createdAt: new Date().toISOString(),
        dueDate: dueDateString
      };

      const task = await dbService.addTask(taskData);
      res.status(201).json({ task });
    } catch (error) {
      res.status(500).json({ error: 'Failed to create task' });
    }
  });

  // PUT /api/tasks/:id - Update task
  router.put('/tasks/:id',
    authenticateToken,
    param('id').isUUID(),
    sanitizeTaskInput,
    body('isCompleted').isBoolean(),
    body('completedAt').optional({ nullable: true }).custom((value) => {
      // Allow null, undefined, or empty string
      if (value === null || value === undefined || value === '') {
        return true;
      }
      // If it's a string, validate it as ISO8601
      if (typeof value === 'string') {
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          throw new Error('completedAt must be a valid ISO8601 date string');
        }
        return true;
      }
      return true; // Allow any other value to pass through
    }),
    handleValidationErrors,
    async (req, res) => {
      try {
        // Handle dueDate whether it's a string or Date object
        let dueDateString;
        if (typeof req.body.dueDate === 'string') {
          dueDateString = req.body.dueDate;
        } else if (req.body.dueDate instanceof Date) {
          dueDateString = req.body.dueDate.toISOString().split('T')[0];
        } else {
          dueDateString = new Date(req.body.dueDate).toISOString().split('T')[0];
        }

        const taskData = {
          ...req.body,
          id: req.params.id,
          dueDate: dueDateString
        };

        const task = await dbService.updateTask(taskData);
        res.json({ task });
      } catch (error) {
        res.status(500).json({ error: 'Failed to update task' });
      }
    }
  );

  // DELETE /api/tasks/:id - Delete task (permanent deletion)
  router.delete('/tasks/:id', authenticateToken, param('id').isUUID(), handleValidationErrors, async (req, res) => {
    try {
      await dbService.deleteTask(req.params.id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: 'Failed to delete task' });
    }
  });

  // GET /api/settings - Get settings
  router.get('/settings', authenticateToken, async (req, res) => {
    try {
      const settings = await dbService.getSettings();
      res.json({ settings });
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch settings' });
    }
  });

  // PUT /api/settings/:key - Update setting
  router.put('/settings/:key',
    authenticateToken,
    param('key').isIn(['showCompleted', 'seeded']),
    body('value').isBoolean(),
    handleValidationErrors,
    async (req, res) => {
      try {
        await dbService.updateSetting(req.params.key, req.body.value);
        res.json({ success: true });
      } catch (error) {
        res.status(500).json({ error: 'Failed to update setting' });
      }
    }
  );

  // POST /api/seed - Seed initial tasks
  router.post('/seed', 
    body('tasks').isArray({ min: 0, max: 50 }),
    body('tasks.*.title').trim().isLength({ min: 1, max: 500 }),
    body('tasks.*.dueDate').isISO8601(),
    body('tasks.*.dueTime').matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    body('tasks.*.priority').isIn(['Low', 'Medium', 'High']),
    handleValidationErrors,
    async (req, res) => {
      try {
        await dbService.seedInitialTasks(req.body.tasks);
        res.json({ success: true });
      } catch (error) {
        res.status(500).json({ error: 'Failed to seed tasks' });
      }
    }
  );

  // Health check endpoint
  router.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
  });

  app.use('/api', router);
}

module.exports = { setupRoutes };