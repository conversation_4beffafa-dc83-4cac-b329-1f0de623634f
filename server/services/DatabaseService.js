const sqlite3 = require('sqlite3');
const path = require('path');
const fs = require('fs/promises');
const crypto = require('crypto');

class DatabaseService {
  constructor() {
    this.db = null;
    // Use environment variable for database path with fallback
    const dbPath = process.env.DATABASE_PATH || path.join(__dirname, '../data/lifetracker.db');
    this.dbPath = path.resolve(dbPath);
    this.vacuumInterval = null;
  }

  async initialize() {
    try {
      // Ensure data directory exists
      const dataDir = path.dirname(this.dbPath);
      await fs.mkdir(dataDir, { recursive: true });

      // Initialize database with WAL mode for better concurrency
      this.db = new sqlite3.Database(this.dbPath);
      
      // Enable WAL mode and foreign keys
      await this.runQuery('PRAGMA journal_mode = WAL');
      await this.runQuery('PRAGMA foreign_keys = ON');
      await this.runQuery('PRAGMA synchronous = NORMAL');
      await this.runQuery('PRAGMA cache_size = 1000');
      await this.runQuery('PRAGMA temp_store = memory');
      
      // Create schema
      await this.createSchema();
      
      // Setup automatic maintenance
      this.setupMaintenanceSchedule();
      
    } catch (error) {
      throw new Error(`Database initialization failed: ${error.message}`);
    }
  }

  async createSchema() {
    const schema = `
      CREATE TABLE IF NOT EXISTS tasks (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        dueDate TEXT NOT NULL,
        dueTime TEXT NOT NULL,
        isCompleted INTEGER NOT NULL DEFAULT 0,
        completedAt TEXT,
        priority TEXT NOT NULL CHECK(priority IN ('Low', 'Medium', 'High')),
        category TEXT,
        isRecurring INTEGER NOT NULL DEFAULT 0,
        recurrenceType TEXT CHECK(recurrenceType IN ('Daily', 'Weekly', 'Monthly', 'Yearly')),
        recurrenceInterval INTEGER,
        createdAt TEXT NOT NULL,
        CONSTRAINT valid_completion CHECK(
          (isCompleted = 0 AND completedAt IS NULL) OR 
          (isCompleted = 1 AND completedAt IS NOT NULL)
        )
      );

      CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL
      );

      CREATE INDEX IF NOT EXISTS idx_tasks_dueDate ON tasks(dueDate);
      CREATE INDEX IF NOT EXISTS idx_tasks_priority ON tasks(priority);
      CREATE INDEX IF NOT EXISTS idx_tasks_isCompleted ON tasks(isCompleted);
      CREATE INDEX IF NOT EXISTS idx_tasks_category ON tasks(category);
    `;

    await this.runQuery(schema);
    
    // Initialize default settings
    await this.runQuery(
      "INSERT OR IGNORE INTO settings (key, value) VALUES ('showCompleted', 'true'), ('seeded', 'false')"
    );
  }

  setupMaintenanceSchedule() {
    // Clear any existing interval
    if (this.vacuumInterval) {
      clearInterval(this.vacuumInterval);
    }
    
    // Run VACUUM every 24 hours
    this.vacuumInterval = setInterval(async () => {
      try {
        await this.vacuum();
        await this.analyze();
        // Force garbage collection if available (in production)
        if (global.gc) {
          global.gc();
        }
      } catch (error) {
        console.error('Database maintenance error:', error);
      }
    }, 24 * 60 * 60 * 1000);
  }

  async vacuum() {
    await this.runQuery('VACUUM');
  }

  async analyze() {
    await this.runQuery('ANALYZE');
  }

  runQuery(sql, params = []) {
    return new Promise((resolve, reject) => {
      if (params.length > 0) {
        this.db.run(sql, params, function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ lastID: this.lastID, changes: this.changes });
          }
        });
      } else {
        this.db.exec(sql, (err) => {
          if (err) {
            reject(err);
          } else {
            resolve(true);
          }
        });
      }
    });
  }

  getQuery(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  async runTransaction(queries) {
    return new Promise((resolve, reject) => {
      this.db.serialize(async () => {
        this.db.run('BEGIN TRANSACTION', (beginErr) => {
          if (beginErr) {
            reject(beginErr);
            return;
          }
          
          const executeQueries = async () => {
            try {
              const results = [];
              for (const { sql, params } of queries) {
                const result = await this.runQuery(sql, params);
                results.push(result);
              }
              return results;
            } catch (error) {
              throw error;
            }
          };
          
          executeQueries()
            .then((results) => {
              this.db.run('COMMIT', (commitErr) => {
                if (commitErr) {
                  // If commit fails, try to rollback
                  this.db.run('ROLLBACK', (rollbackErr) => {
                    if (rollbackErr) {
                      console.error('Critical: Both commit and rollback failed', { commitErr, rollbackErr });
                      // Create compound error with both failures
                      const compoundError = new Error(`Transaction failed: ${commitErr.message}. Rollback also failed: ${rollbackErr.message}`);
                      compoundError.commitError = commitErr;
                      compoundError.rollbackError = rollbackErr;
                      reject(compoundError);
                    } else {
                      console.warn('Transaction committed failed but rollback succeeded');
                      reject(commitErr);
                    }
                  });
                } else {
                  resolve(results);
                }
              });
            })
            .catch((error) => {
              this.db.run('ROLLBACK', (rollbackErr) => {
                if (rollbackErr) {
                  console.error('Query execution failed and rollback failed:', { error, rollbackErr });
                  // Create compound error with both failures
                  const compoundError = new Error(`Query failed: ${error.message}. Rollback also failed: ${rollbackErr.message}`);
                  compoundError.queryError = error;
                  compoundError.rollbackError = rollbackErr;
                  reject(compoundError);
                } else {
                  console.warn('Query execution failed but rollback succeeded');
                  reject(error);
                }
              });
            });
        });
      });
    });
  }

  // Task operations
  async getTasks() {
    const rows = await this.getQuery('SELECT * FROM tasks ORDER BY dueDate, dueTime');
    return rows.map(this.rowToTask);
  }

  async addTask(task) {
    const sql = `INSERT INTO tasks (id, title, description, dueDate, dueTime, isCompleted, completedAt, priority, category, isRecurring, recurrenceType, recurrenceInterval, createdAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    const params = [
      task.id,
      task.title,
      task.description || null,
      task.dueDate,
      task.dueTime,
      task.isCompleted ? 1 : 0,
      task.completedAt || null,
      task.priority,
      task.category || null,
      task.isRecurring ? 1 : 0,
      task.recurrenceType || null,
      task.recurrenceInterval || null,
      task.createdAt
    ];
    await this.runQuery(sql, params);
    return task;
  }

  async updateTask(task) {
    const sql = `UPDATE tasks SET title = ?, description = ?, dueDate = ?, dueTime = ?, isCompleted = ?, completedAt = ?, priority = ?, category = ?, isRecurring = ?, recurrenceType = ?, recurrenceInterval = ? WHERE id = ?`;
    const params = [
      task.title,
      task.description || null,
      task.dueDate,
      task.dueTime,
      task.isCompleted ? 1 : 0,
      task.completedAt || null,
      task.priority,
      task.category || null,
      task.isRecurring ? 1 : 0,
      task.recurrenceType || null,
      task.recurrenceInterval || null,
      task.id
    ];
    await this.runQuery(sql, params);
    return task;
  }

  async deleteTask(taskId) {
    await this.runQuery('DELETE FROM tasks WHERE id = ?', [taskId]);
    // Force immediate VACUUM after deletion for space reclamation
    await this.vacuum();
  }

  // Settings operations
  async getSettings() {
    const rows = await this.getQuery('SELECT key, value FROM settings');
    const settings = { showCompleted: true, seeded: false };
    rows.forEach(row => {
      if (row.key === 'showCompleted' || row.key === 'seeded') {
        settings[row.key] = row.value === 'true';
      }
    });
    return settings;
  }

  async updateSetting(key, value) {
    await this.runQuery('INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)', [key, String(value)]);
  }

  async seedInitialTasks(tasksToSeed) {
    const settings = await this.getSettings();
    if (settings.seeded) return;
    
    const tasks = await this.getTasks();
    if (tasks.length > 0) {
      await this.updateSetting('seeded', true);
      return;
    }

    const queries = [];
    for (const taskData of tasksToSeed) {
      const task = {
        ...taskData,
        id: crypto.randomUUID(),
        isCompleted: false,
        completedAt: undefined,
        createdAt: new Date().toISOString(),
      };
      
      queries.push({
        sql: `INSERT INTO tasks (id, title, description, dueDate, dueTime, isCompleted, completedAt, priority, category, isRecurring, recurrenceType, recurrenceInterval, createdAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        params: [
          task.id, task.title, task.description || null, task.dueDate, task.dueTime,
          0, null, task.priority, task.category || null,
          task.isRecurring ? 1 : 0, task.recurrenceType || null, task.recurrenceInterval || null, task.createdAt
        ]
      });
    }
    
    queries.push({
      sql: "INSERT OR REPLACE INTO settings (key, value) VALUES ('seeded', 'true')",
      params: []
    });

    await this.runTransaction(queries);
  }

  rowToTask(row) {
    return {
      id: row.id,
      title: row.title,
      description: row.description,
      dueDate: row.dueDate,
      dueTime: row.dueTime,
      isCompleted: row.isCompleted === 1,
      completedAt: row.completedAt,
      priority: row.priority,
      category: row.category,
      isRecurring: row.isRecurring === 1,
      recurrenceType: row.recurrenceType,
      recurrenceInterval: row.recurrenceInterval,
      createdAt: row.createdAt,
    };
  }

  async close() {
    if (this.vacuumInterval) {
      clearInterval(this.vacuumInterval);
    }
    
    if (this.db) {
      return new Promise((resolve) => {
        this.db.close((err) => {
          resolve();
        });
      });
    }
  }
}

module.exports = { DatabaseService };