const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// In production, this should be in environment variables
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
const JWT_EXPIRES_IN = '24h';

// For demo purposes, we'll use a simple in-memory user store
// In production, this should be in a proper database
const users = [
  {
    id: '1',
    username: 'scharway',
    // This is the hashed version of 'Lookup88?'
    passwordHash: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // Will be set properly
    email: '<EMAIL>',
    createdAt: new Date().toISOString()
  }
];

class AuthService {
  constructor() {
    this.initialized = false;
    this.initializeDefaultUser();
  }

  async initializeDefaultUser() {
    // Hash the default password properly
    const defaultPassword = 'Lookup88?';
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(defaultPassword, saltRounds);

    // Update the default user with properly hashed password
    users[0].passwordHash = hashedPassword;
    this.initialized = true;
  }

  async ensureInitialized() {
    if (!this.initialized) {
      await this.initializeDefaultUser();
    }
  }

  async validateCredentials(username, password) {
    await this.ensureInitialized();

    try {
      const user = users.find(u => u.username === username);
      if (!user) {
        return null;
      }

      const isValidPassword = await bcrypt.compare(password, user.passwordHash);
      if (!isValidPassword) {
        return null;
      }

      // Return user without password hash
      const { passwordHash, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      console.error('Error validating credentials:', error);
      return null;
    }
  }

  generateToken(user) {
    try {
      const payload = {
        userId: user.id,
        username: user.username,
        email: user.email
      };

      return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
    } catch (error) {
      console.error('Error generating token:', error);
      throw new Error('Failed to generate authentication token');
    }
  }

  verifyToken(token) {
    try {
      return jwt.verify(token, JWT_SECRET);
    } catch (error) {
      console.error('Error verifying token:', error);
      return null;
    }
  }

  async changePassword(userId, currentPassword, newPassword) {
    try {
      const user = users.find(u => u.id === userId);
      if (!user) {
        throw new Error('User not found');
      }

      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash);
      if (!isCurrentPasswordValid) {
        throw new Error('Current password is incorrect');
      }

      const saltRounds = 10;
      const newHashedPassword = await bcrypt.hash(newPassword, saltRounds);
      user.passwordHash = newHashedPassword;

      return true;
    } catch (error) {
      console.error('Error changing password:', error);
      throw error;
    }
  }

  // Get user by ID (for token validation)
  getUserById(userId) {
    const user = users.find(u => u.id === userId);
    if (user) {
      const { passwordHash, ...userWithoutPassword } = user;
      return userWithoutPassword;
    }
    return null;
  }
}

module.exports = new AuthService();
