const crypto = require('crypto');

/**
 * Enhanced logging utility with correlation IDs for request tracing
 */
class Logger {
  constructor() {
    this.logLevels = {
      ERROR: 0,
      WARN: 1,
      INFO: 2,
      DEBUG: 3
    };
    this.currentLevel = this.logLevels[process.env.LOG_LEVEL || 'INFO'];
  }

  generateCorrelationId() {
    return crypto.randomUUID();
  }

  formatLog(level, message, correlationId = null, metadata = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      correlationId,
      ...metadata
    };

    // Add process and environment info for production debugging
    if (level === 'ERROR') {
      logEntry.processId = process.pid;
      logEntry.nodeVersion = process.version;
      logEntry.environment = process.env.NODE_ENV || 'development';
    }

    return JSON.stringify(logEntry);
  }

  error(message, correlationId = null, metadata = {}) {
    if (this.currentLevel >= this.logLevels.ERROR) {
      console.error(this.formatLog('ERROR', message, correlationId, metadata));
    }
  }

  warn(message, correlationId = null, metadata = {}) {
    if (this.currentLevel >= this.logLevels.WARN) {
      console.warn(this.formatLog('WARN', message, correlationId, metadata));
    }
  }

  info(message, correlationId = null, metadata = {}) {
    if (this.currentLevel >= this.logLevels.INFO) {
      console.info(this.formatLog('INFO', message, correlationId, metadata));
    }
  }

  debug(message, correlationId = null, metadata = {}) {
    if (this.currentLevel >= this.logLevels.DEBUG) {
      console.debug(this.formatLog('DEBUG', message, correlationId, metadata));
    }
  }

  // Request-specific logging helpers
  logRequest(req, correlationId) {
    this.info('Request received', correlationId, {
      method: req.method,
      url: req.url,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      userId: req.user?.userId || null
    });
  }

  logResponse(req, res, correlationId, duration) {
    this.info('Request completed', correlationId, {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      userId: req.user?.userId || null
    });
  }

  logError(error, correlationId, context = {}) {
    this.error(error.message, correlationId, {
      stack: error.stack,
      name: error.name,
      ...context
    });
  }

  logAuth(action, result, correlationId, metadata = {}) {
    this.info(`Auth ${action}`, correlationId, {
      result,
      ...metadata
    });
  }

  logDatabase(operation, correlationId, metadata = {}) {
    this.debug(`Database ${operation}`, correlationId, metadata);
  }
}

module.exports = new Logger();