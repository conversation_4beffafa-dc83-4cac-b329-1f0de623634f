const logger = require('../utils/logger');

/**
 * Centralized error handling middleware
 * This should be the last middleware in the chain
 */
const errorHandler = (err, req, res, next) => {
  const correlationId = req.correlationId || 'unknown';
  
  // Log the error with full context
  logger.logError(err, correlationId, {
    method: req.method,
    url: req.url,
    body: req.body,
    params: req.params,
    query: req.query,
    userId: req.user?.userId || null,
    userAgent: req.get('User-Agent'),
    ip: req.ip || req.connection.remoteAddress
  });

  // Don't expose internal errors in production
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  // Determine error type and response
  let statusCode = 500;
  let message = 'Internal server error';
  let details = null;

  if (err.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation error';
    details = isDevelopment ? err.message : 'Invalid input data';
  } else if (err.name === 'UnauthorizedError' || err.message.includes('token')) {
    statusCode = 401;
    message = 'Unauthorized';
    details = 'Invalid or expired authentication token';
  } else if (err.name === 'ForbiddenError') {
    statusCode = 403;
    message = 'Forbidden';
    details = 'Insufficient permissions';
  } else if (err.name === 'NotFoundError') {
    statusCode = 404;
    message = 'Not found';
    details = 'The requested resource was not found';
  } else if (err.name === 'ConflictError') {
    statusCode = 409;
    message = 'Conflict';
    details = isDevelopment ? err.message : 'Resource conflict';
  } else if (err.statusCode) {
    statusCode = err.statusCode;
    message = err.message || message;
  }

  const errorResponse = {
    error: message,
    correlationId,
    timestamp: new Date().toISOString()
  };

  // Add details if available and appropriate
  if (details) {
    errorResponse.details = details;
  }

  // Add stack trace in development
  if (isDevelopment && err.stack) {
    errorResponse.stack = err.stack;
  }

  res.status(statusCode).json(errorResponse);
};

/**
 * 404 handler for unmatched routes
 */
const notFoundHandler = (req, res, next) => {
  const correlationId = req.correlationId || 'unknown';
  
  logger.warn('Route not found', correlationId, {
    method: req.method,
    url: req.url,
    ip: req.ip || req.connection.remoteAddress
  });

  res.status(404).json({
    error: 'Not found',
    message: 'The requested endpoint does not exist',
    correlationId,
    timestamp: new Date().toISOString()
  });
};

/**
 * Async error wrapper to catch errors in async route handlers
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

module.exports = {
  errorHandler,
  notFoundHandler,
  asyncHandler
};