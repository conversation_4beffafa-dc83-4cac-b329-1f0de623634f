import React, { useState, useContext } from 'react';
import Sidebar from './Sidebar';
import TasksView from '../../views/TasksView';
import SettingsView from '../../views/SettingsView';
import ProfileView from '../../views/ProfileView';
import { View } from '../../types';
import { TaskContext } from '../../contexts/TaskContext';
import Header from './Header';

const MainLayout: React.FC = () => {
    const [currentView, setCurrentView] = useState<View>('tasks');
    const taskContext = useContext(TaskContext);

    if (!taskContext) throw new Error("TaskContext not found");

    const { isSeeding, error, retryLoading } = taskContext;

    const renderView = () => {
        switch (currentView) {
            case 'tasks':
                return <TasksView />;
            case 'settings':
                return <SettingsView />;
            case 'profile':
                return <ProfileView />;
            default:
                return <TasksView />;
        }
    };
    
    if (isSeeding) {
        return (
            <div className="flex items-center justify-center h-screen bg-gray-50">
                <div className="flex flex-col items-center max-w-md mx-auto text-center">
                    <div className="w-12 h-12 border-4 border-brand-secondary border-t-transparent rounded-full animate-spin"></div>
                    <p className="mt-4 text-brand-secondary">Loading your tasks...</p>
                    {error && (
                        <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                            <p className="text-red-800 text-sm font-medium mb-2">Loading Error</p>
                            <p className="text-red-600 text-sm mb-3">{error}</p>
                            <button
                                onClick={retryLoading}
                                className="px-4 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors"
                            >
                                Retry Loading
                            </button>
                        </div>
                    )}
                    <p className="mt-4 text-gray-500 text-sm">
                        If this takes more than 10 seconds, try refreshing the page
                    </p>
                </div>
            </div>
        );
    }

    // Show global error if there's an error but we're not loading
    if (error && !isSeeding) {
        return (
            <div className="flex items-center justify-center h-screen bg-gray-50">
                <div className="flex flex-col items-center max-w-md mx-auto text-center">
                    <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                        <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                    </div>
                    <h2 className="text-xl font-semibold text-gray-900 mb-2">Something went wrong</h2>
                    <p className="text-gray-600 text-sm mb-4">{error}</p>
                    <button
                        onClick={retryLoading}
                        className="px-6 py-2 bg-brand-secondary text-white rounded-md hover:bg-opacity-90 transition-colors"
                    >
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="flex h-screen bg-gray-50 text-brand-primary">
            <Sidebar currentView={currentView} setCurrentView={setCurrentView} />
            <div className="flex-1 flex flex-col overflow-hidden">
                <Header 
                    currentView={currentView} 
                />
                <main className="flex-1 overflow-y-auto custom-scrollbar bg-gray-50">
                    <div className="p-4 sm:p-6 lg:p-8">
                        {renderView()}
                    </div>
                </main>
            </div>
        </div>
    );
};

export default MainLayout;