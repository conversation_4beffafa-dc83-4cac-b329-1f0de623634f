/**
 * Browser-compatible UUID generation utility
 * Falls back to mathematical UUID generation when crypto.randomUUID is unavailable
 */

/**
 * Generates a RFC 4122 compliant UUID v4
 * Uses crypto.randomUUID when available, falls back to math-based generation
 */
export function generateUUID(): string {
  // Check if crypto.randomUUID is available (modern browsers, HTTPS contexts)
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    try {
      return crypto.randomUUID();
    } catch (error) {
      console.warn('crypto.randomUUID failed, falling back to mathematical generation:', error);
    }
  }

  // Fallback: Mathematical UUID generation using Math.random()
  // This generates RFC 4122 compliant UUIDs with proper bit manipulation
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Validates if a string is a valid UUID format
 */
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}