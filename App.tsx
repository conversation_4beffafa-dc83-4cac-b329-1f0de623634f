import React, { useContext } from 'react';
import { AuthProvider, AuthContext } from './contexts/AuthContext';
import { SettingsProvider } from './contexts/SettingsContext';
import { TaskProvider } from './contexts/TaskContext';
import LoginPage from './components/auth/LoginPage';
import MainLayout from './components/layout/MainLayout';

const AppContent: React.FC = () => {
    const authContext = useContext(AuthContext);

    if (!authContext) {
        throw new Error("AuthContext must be used within an AuthProvider");
    }

    const { isAuthenticated, isInitializing } = authContext;
    
    if (isInitializing) {
        return (
            <div className="flex items-center justify-center h-screen bg-gray-50">
                <div className="flex flex-col items-center">
                    <div className="w-12 h-12 border-4 border-brand-secondary border-t-transparent rounded-full animate-spin"></div>
                    <p className="mt-4 text-brand-secondary">Loading application...</p>
                </div>
            </div>
        );
    }

    return isAuthenticated ? <MainLayout /> : <LoginPage />;
};

const App: React.FC = () => {
  return (
    <AuthProvider>
      <SettingsProvider>
        <TaskProvider>
          <AppContent />
        </TaskProvider>
      </SettingsProvider>
    </AuthProvider>
  );
};

export default App;