export enum Priority {
    Low = 'Low',
    Medium = 'Medium',
    High = 'High'
}

export enum RecurrenceType {
    Daily = 'Daily',
    Weekly = 'Weekly',
    Monthly = 'Monthly',
    Yearly = 'Yearly'
}

export interface Task {
    id: string;
    title: string;
    description?: string;
    dueDate: string; // YYYY-MM-DD
    dueTime: string; // HH:mm
    isCompleted: boolean;
    completedAt?: string; // ISO string
    priority: Priority;
    category?: string;
    isRecurring: boolean;
    recurrenceType?: RecurrenceType;
    recurrenceInterval?: number;
    createdAt: string; // ISO string
}

export interface WeatherData {
    locationName: string;
    current: {
        temperature: number;
        weatherCode: number;
        feelsLike: number;
    };
    forecast: {
        date: string; // YYYY-MM-DD
        weatherCode: number;
        tempHigh: number;
        tempLow: number;
        precipitation?: number;
    }[];
}

export type View = 'tasks' | 'settings' | 'profile';

export interface Settings {
    showCompleted: boolean;
    seeded: boolean;
}

export interface TaskContextType {
    tasks: Task[];
    addTask: (task: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>) => Promise<void>;
    updateTask: (updatedTask: Task) => Promise<void>;
    deleteTask: (taskId: string) => Promise<void>;
    isSeeding: boolean;
    error: string | null;
    retryLoading: () => Promise<void>;
}
