import { Task, Settings, Priority, RecurrenceType } from '../types';

const API_BASE_URL = '/api';

class ApiError extends Error {
  constructor(message: string, public status: number) {
    super(message);
    this.name = 'ApiError';
  }
}

async function apiRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    credentials: 'include', // Include cookies for authentication
    ...options,
  });

  if (!response.ok) {
    let errorMessage = 'API request failed';
    try {
      const errorData = await response.json();
      errorMessage = errorData.error || errorData.message || response.statusText;
    } catch {
      errorMessage = response.statusText || `HTTP ${response.status}`;
    }
    throw new ApiError(errorMessage, response.status);
  }

  return response.json();
}

export const apiService = {
  isInitialized: true, // Always true for API service

  async initialize(): Promise<void> {
    // No initialization needed for API service - removed redundant functionality
    return Promise.resolve();
  },

  async getSettings(): Promise<Settings> {
    const response = await apiRequest<{ settings: Settings }>('/settings');
    return response.settings;
  },

  async updateSetting(key: keyof Settings, value: string | boolean): Promise<void> {
    await apiRequest(`/settings/${key}`, {
      method: 'PUT',
      body: JSON.stringify({ value }),
    });
  },

  async getTasks(): Promise<Task[]> {
    const response = await apiRequest<{ tasks: Task[] }>('/tasks');
    return response.tasks;
  },

  async addTask(task: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>): Promise<Task> {
    const response = await apiRequest<{ task: Task }>('/tasks', {
      method: 'POST',
      body: JSON.stringify(task),
    });
    return response.task;
  },

  async updateTask(task: Task): Promise<void> {
    await apiRequest(`/tasks/${task.id}`, {
      method: 'PUT',
      body: JSON.stringify(task),
    });
  },

  async deleteTask(taskId: string): Promise<void> {
    await apiRequest(`/tasks/${taskId}`, {
      method: 'DELETE',
    });
  },

  async seedInitialTasks(tasksToSeed: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>[]): Promise<void> {
    await apiRequest('/seed', {
      method: 'POST',
      body: JSON.stringify({ tasks: tasksToSeed }),
    });
  },

  // Transaction wrapper for complex operations
  async runInTransaction<T>(operation: () => Promise<T>): Promise<T> {
    // Client-side transaction resilience with error recovery
    // Since true database transactions require backend support,
    // we implement client-side error handling and state recovery
    try {
      return await operation();
    } catch (error) {
      console.error('Transaction operation failed:', error);
      
      // For operations that may have partially succeeded,
      // we could implement compensation logic here
      // For now, re-throw to let caller handle the failure
      throw new Error(`Transaction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  // Add a full task (used for recurring task instances)
  async addFullTask(task: Task): Promise<Task> {
    const response = await apiRequest<{ task: Task }>('/tasks', {
      method: 'POST',
      body: JSON.stringify(task),
    });
    return response.task;
  }
};