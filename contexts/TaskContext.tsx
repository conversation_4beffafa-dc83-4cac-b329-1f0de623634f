import React, { createContext, ReactNode, useState, useEffect, useCallback } from 'react';
import { Task, Priority, RecurrenceType, TaskContextType } from '../types';
import { getNextRecurrenceDate } from '../utils/dateUtils';
import { apiService } from '../services/apiService';

export const TaskContext = createContext<TaskContextType | undefined>(undefined);

const initialSeedTasks: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>[] = [
    {
        title: "Renew Gym Membership",
        dueDate: "2024-08-15",
        dueTime: "10:00",
        isRecurring: true,
        recurrenceType: RecurrenceType.Yearly,
        recurrenceInterval: 1,
        category: 'Health',
        priority: Priority.Medium,
    },
    {
        title: "Monthly Project Report",
        dueDate: "2024-09-05",
        dueTime: "17:00",
        isRecurring: true,
        recurrenceType: RecurrenceType.Monthly,
        recurrenceInterval: 1,
        category: 'Work',
        priority: Priority.High,
    }
];


export const TaskProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [tasks, setTasks] = useState<Task[]>([]);
    const [isSeeding, setIsSeeding] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const loadAndSeedTasks = useCallback(async () => {
        setIsSeeding(true);
        setError(null);
        
        // Add timeout to prevent infinite loading
        const timeoutId = setTimeout(() => {
            console.error('Task loading timeout - setting isSeeding to false');
            setIsSeeding(false);
            setError('Failed to load tasks: Request timeout');
        }, 10000); // 10 second timeout
        
        try {
            await apiService.seedInitialTasks(initialSeedTasks);
            const dbTasks = await apiService.getTasks();
            setTasks(dbTasks);
            clearTimeout(timeoutId);
            setIsSeeding(false);
        } catch (error) {
            clearTimeout(timeoutId);
            console.error('Failed to load tasks:', error);
            setError(error instanceof Error ? error.message : 'Failed to load tasks');
            setIsSeeding(false);
            // Set empty tasks array as fallback
            setTasks([]);
        }
    }, []);

    useEffect(() => {
        if (apiService.isInitialized) {
            loadAndSeedTasks();
        }
    }, [loadAndSeedTasks]);

    const refreshTasks = async () => {
        try {
            const updatedTasks = await apiService.getTasks();
            setTasks(updatedTasks);
            setError(null);
        } catch (error) {
            console.error('Failed to refresh tasks:', error);
            setError(error instanceof Error ? error.message : 'Failed to refresh tasks');
        }
    };

    const addTask = async (task: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>) => {
        try {
            await apiService.addTask(task);
            await refreshTasks();
        } catch (error) {
            console.error('Failed to add task:', error);
            setError(error instanceof Error ? error.message : 'Failed to add task');
            throw error; // Re-throw so UI can handle it
        }
    };
    
    const updateTask = async (updatedTask: Task) => {
        try {
            if (updatedTask.isRecurring && updatedTask.isCompleted) {
                const originalTask = tasks.find(t => t.id === updatedTask.id);
                if(originalTask) {
                    // Use a transaction to ensure both operations succeed or fail together
                    await apiService.runInTransaction(async () => {
                        // 1. Update the original recurring task for its next occurrence
                        const nextDueDate = getNextRecurrenceDate(originalTask);
                        await apiService.updateTask({ ...originalTask, dueDate: nextDueDate });
                        
                        // 2. Create a new, non-recurring, completed task instance
                        const completedInstance: Task = {
                            ...originalTask,
                            id: crypto.randomUUID(),
                            isRecurring: false,
                            isCompleted: true,
                            completedAt: new Date().toISOString(),
                            createdAt: new Date().toISOString(), 
                        };
                        await apiService.addFullTask(completedInstance);
                    });
                }
            } else {
                // Standard update for non-recurring tasks or other field changes
                const taskToSave = { ...updatedTask, completedAt: updatedTask.isCompleted ? new Date().toISOString() : undefined };
                await apiService.updateTask(taskToSave);
            }
            await refreshTasks();
        } catch (error) {
            console.error('Failed to update task:', error);
            setError(error instanceof Error ? error.message : 'Failed to update task');
            throw error; // Re-throw so UI can handle it
        }
    };

    const deleteTask = async (taskId: string) => {
        try {
            await apiService.deleteTask(taskId);
            await refreshTasks();
        } catch (error) {
            console.error('Failed to delete task:', error);
            setError(error instanceof Error ? error.message : 'Failed to delete task');
            throw error; // Re-throw so UI can handle it
        }
    };
    
    const value = { tasks, addTask, updateTask, deleteTask, isSeeding, error, retryLoading: loadAndSeedTasks };

    return (
        <TaskContext.Provider value={value}>
            {children}
        </TaskContext.Provider>
    );
};