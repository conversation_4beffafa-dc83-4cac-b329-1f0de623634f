
import React, { createContext, useState, useCallback, useEffect, ReactNode } from 'react';

interface User {
    id: string;
    username: string;
    email: string;
}

interface AuthContextType {
    isAuthenticated: boolean;
    isInitializing: boolean;
    user: User | null;
    login: (username: string, password: string) => Promise<void>;
    logout: () => Promise<void>;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
    const [isInitializing, setIsInitializing] = useState<boolean>(true);
    const [user, setUser] = useState<User | null>(null);

    // Check authentication status on app load
    useEffect(() => {
        const checkAuthStatus = async () => {
            try {
                const response = await fetch('/api/auth/verify', {
                    method: 'GET',
                    credentials: 'include', // Include cookies
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.authenticated && data.user) {
                        setIsAuthenticated(true);
                        setUser(data.user);
                    }
                }
            } catch (error) {
                console.error('Auth verification failed:', error);
                // Not authenticated, which is fine
            } finally {
                setIsInitializing(false);
            }
        };

        checkAuthStatus();
    }, []);

    const login = useCallback(async (username: string, password: string): Promise<void> => {
        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                credentials: 'include', // Include cookies
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Login failed');
            }

            if (data.success && data.user) {
                setIsAuthenticated(true);
                setUser(data.user);
            } else {
                throw new Error('Invalid response from server');
            }
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    }, []);

    const logout = useCallback(async (): Promise<void> => {
        try {
            await fetch('/api/auth/logout', {
                method: 'POST',
                credentials: 'include', // Include cookies
                headers: {
                    'Content-Type': 'application/json',
                },
            });
        } catch (error) {
            console.error('Logout error:', error);
            // Continue with logout even if API call fails
        } finally {
            setIsAuthenticated(false);
            setUser(null);
        }
    }, []);

    const value = { isAuthenticated, isInitializing, user, login, logout };

    return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
